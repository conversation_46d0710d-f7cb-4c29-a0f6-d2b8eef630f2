package com.yxt.talent.rv.application.authprj;

import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireBaseInfo;
import com.yxt.spsdfacade.bean.spsd.ModelRequireDetailInfo;
import com.yxt.talent.rv.application.aom.CommonAomService;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjDimGroupDTO;
import com.yxt.talent.rv.application.authprj.dto.AuthPrjIndicatorDetailDTO;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvExtBO;
import com.yxt.talent.rv.application.xpd.common.dto.AomActvIndicatorDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.AuthprjMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.AuthprjPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 认证项目指标层级处理服务
 * 提供指标层级关系处理、维度分组等共同功能
 */
@Slf4j
@Service
@AllArgsConstructor
public class AuthPrjIndicatorHierarchyService {

    private final AuthprjMapper authprjMapper;
    private final CommonAomService commonAomService;
    private final SpsdAclService spsdAclService;

    /**
     * 指标层级信息
     */
    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IndicatorHierarchyInfo {
        private String dimId;           // 所属维度ID
        private String dimName;         // 所属维度名称
        private String firstIndicatorId;    // 一级指标ID
        private String firstIndicatorName;  // 一级指标名称
        private String secondIndicatorId;   // 二级指标ID
        private String secondIndicatorName; // 二级指标名称
        private String thirdIndicatorId;    // 三级指标ID
        private String thirdIndicatorName;  // 三级指标名称
    }

    /**
     * 指标处理上下文信息
     */
    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IndicatorProcessContext {
        private AuthprjPO authPrj;                          // 认证项目信息
        private List<AomActvExtBO> activityList;            // 活动列表
        private Set<String> indicatorIds;                   // 指标ID集合
        private ModelInfo modelInfoDTO;                     // 模型信息
        private Map<String, AomActvExtBO> indicatorToActivityMap;  // 指标到活动的映射
        private Map<String, ModelRequireBaseInfo> dimMap;   // 维度映射
    }

    /**
     * 初始化指标处理上下文
     *
     * @param orgId     机构ID
     * @param authPrjId 认证项目ID
     * @return 处理上下文，如果初始化失败返回null
     */
    @Nullable
    public IndicatorProcessContext initProcessContext(String orgId, String authPrjId) {
        log.info("初始化指标处理上下文: orgId={}, authPrjId={}", orgId, authPrjId);

        // 获取认证项目信息
        AuthprjPO authprj = authprjMapper.selectByIdAndOrg(orgId, authPrjId);
        if (authprj == null) {
            log.warn("认证项目不存在: orgId={}, authPrjId={}", orgId, authPrjId);
            return null;
        }

        // 获取项目下的所有活动指标信息，
        // 【注意】这里与盘点不一样的地方：活动（尤其是考试活动）上报的指标可能和认证项目下指定的模型中的指标完全不一样，而概览和个人报告又要求只展示模型中的指标，所以需要取个交集
        List<AomActvExtBO> activityList = commonAomService.activityIndicatorList(orgId, authprj.getAomPrjId());
        if (CollectionUtils.isEmpty(activityList)) {
            log.info("认证项目下没有活动指标信息: orgId={}, authPrjId={}, aomPrjId={}",
                    orgId, authPrjId, authprj.getAomPrjId());
            return null;
        }

        // 收集所有指标ID
        Set<String> activityIndicatorIds = collectIndicatorIds(activityList);
        if (activityIndicatorIds.isEmpty()) {
            log.info("认证项目下没有指标信息: orgId={}, authPrjId={}", orgId, authPrjId);
            return null;
        }

        // 获取模型信息
        ModelInfo spsdModelInfo = spsdAclService.getModelInfo(orgId, authprj.getModelId());
        if (spsdModelInfo == null || CollectionUtils.isEmpty(spsdModelInfo.getDms())) {
            log.warn("模型信息为空: orgId={}, modelId={}", orgId, authprj.getModelId());
            return null;
        }

        // 直接使用模型信息
        // 收集模型中定义的所有指标ID
        Set<String> modelIndicatorIds = collectModelIndicatorIds(spsdModelInfo.getDms());
        if (modelIndicatorIds.isEmpty()) {
            log.warn("模型中没有定义指标: orgId={}, modelId={}", orgId, authprj.getModelId());
            return null;
        }

        // 计算活动指标与模型指标的交集
        // 【注意】这里与盘点不一样的地方：活动（尤其是考试活动）上报的指标可能和认证项目下指定的模型中的指标完全不一样，而概览和个人报告又要求只展示模型中的指标，所以需要取个交集
        Set<String> indicatorIds = intersectIndicatorIds(activityIndicatorIds, modelIndicatorIds);
        if (indicatorIds.isEmpty()) {
            log.warn("活动指标与模型指标无交集: orgId={}, authPrjId={}, activityCount={}, modelCount={}", 
                    orgId, authPrjId, activityIndicatorIds.size(), modelIndicatorIds.size());
            return null;
        }

        log.info(
            "LOG42006:指标交集处理完成: orgId={}, authPrjId={}, 活动指标数={}, 模型指标数={}, 交集指标数={}",
                orgId, authPrjId, activityIndicatorIds.size(), modelIndicatorIds.size(), indicatorIds.size());

        // 创建指标ID到活动的映射（只包含交集中的指标）
        Map<String, AomActvExtBO> indicatorToActivityMap = createIndicatorActivityMap(activityList, indicatorIds);

        // 平铺所有的维度信息集合，用于名称组装和查找
        List<ModelRequireBaseInfo> allDims = new ArrayList<>();
        recursiveInitAllDims(allDims, spsdModelInfo.getDms());

        // 创建维度ID到维度信息的映射
        Map<String, ModelRequireBaseInfo> dimMap = allDims.stream()
                .collect(Collectors.toMap(ModelRequireBaseInfo::getDmId, Function.identity(), (a, b) -> a));

        return new IndicatorProcessContext(authprj, activityList, indicatorIds, spsdModelInfo,
                indicatorToActivityMap, dimMap);
    }

    /**
     * 收集所有指标ID
     */
    public Set<String> collectIndicatorIds(List<AomActvExtBO> activityList) {
        Set<String> indicatorIds = new HashSet<>();
        for (AomActvExtBO activity : activityList) {
            if (CollectionUtils.isNotEmpty(activity.getIndicators())) {
                for (AomActvIndicatorDto indicator : activity.getIndicators()) {
                    if (StringUtils.isNotBlank(indicator.getSdIndicatorId())) {
                        indicatorIds.add(indicator.getSdIndicatorId());
                    }
                }
            }
        }
        return indicatorIds;
    }

    /**
     * 收集模型中定义的所有指标ID
     */
    public Set<String> collectModelIndicatorIds(List<ModelRequireBaseInfo> dms) {
        Set<String> indicatorIds = new HashSet<>();
        if (CollectionUtils.isEmpty(dms)) {
            return indicatorIds;
        }

        for (ModelRequireBaseInfo dm : dms) {
            if (CollectionUtils.isNotEmpty(dm.getDetails())) {
                for (ModelRequireDetailInfo detail : dm.getDetails()) {
                    if (StringUtils.isNotBlank(detail.getItemId())) {
                        indicatorIds.add(detail.getItemId());
                    }
                    if (CollectionUtils.isNotEmpty(detail.getChilds())) {
                        indicatorIds.addAll(collectSubIndicatorIds(detail.getChilds()));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(dm.getChilds())) {
                indicatorIds.addAll(collectModelIndicatorIds(dm.getChilds()));
            }
        }
        return indicatorIds;
    }

    /**
     * 递归收集子指标ID
     */
    private Set<String> collectSubIndicatorIds(List<ModelRequireDetailInfo> children) {
        Set<String> indicatorIds = new HashSet<>();
        if (CollectionUtils.isEmpty(children)) {
            return indicatorIds;
        }

        for (ModelRequireDetailInfo child : children) {
            if (StringUtils.isNotBlank(child.getItemId())) {
                indicatorIds.add(child.getItemId());
            }
            if (CollectionUtils.isNotEmpty(child.getChilds())) {
                indicatorIds.addAll(collectSubIndicatorIds(child.getChilds()));
            }
        }
        return indicatorIds;
    }

    /**
     * 计算两个指标集合的交集
     */
    public Set<String> intersectIndicatorIds(Set<String> indicatorIds1, Set<String> indicatorIds2) {
        Set<String> intersection = new HashSet<>(indicatorIds1);
        intersection.retainAll(indicatorIds2);
        return intersection;
    }

    /**
     * 创建指标到活动的映射
     */
    public Map<String, AomActvExtBO> createIndicatorActivityMap(List<AomActvExtBO> activityList, Set<String> indicatorIds) {
        Map<String, AomActvExtBO> map = new HashMap<>();
        for (AomActvExtBO activity : activityList) {
            if (CollectionUtils.isNotEmpty(activity.getIndicators())) {
                for (AomActvIndicatorDto indicator : activity.getIndicators()) {
                    if (StringUtils.isNotBlank(indicator.getSdIndicatorId()) && 
                        indicatorIds.contains(indicator.getSdIndicatorId())) {
                        // 如果同一指标有多个活动，取创建时间最新的
                        AomActvExtBO existing = map.get(indicator.getSdIndicatorId());
                        if (existing == null) {
                            map.put(indicator.getSdIndicatorId(), activity);
                        } else if (activity.getCreateTime() != null && existing.getCreateTime() == null) {
                            map.put(indicator.getSdIndicatorId(), activity);
                        } else if (activity.getCreateTime() != null && activity.getCreateTime().after(existing.getCreateTime())) {
                            map.put(indicator.getSdIndicatorId(), activity);
                        }
                    }
                }
            }
        }
        return map;
    }

    /**
     * 平铺所有的维度信息集合，用于名称组装
     */
    public void recursiveInitAllDims(List<ModelRequireBaseInfo> allDims, List<ModelRequireBaseInfo> dms) {
        if (CollectionUtils.isEmpty(dms)) {
            return;
        }
        dms.forEach(dmItem -> {
            allDims.add(dmItem);
            //级别类型，0-父级，1-末级
            if (Integer.valueOf(0).equals(dmItem.getLevelType()) && CollectionUtils.isNotEmpty(dmItem.getChilds())) {
                recursiveInitAllDims(allDims, dmItem.getChilds());
            }
        });
    }

    /**
     * 通过模型信息查找指标的层级关系
     */
    @Nullable
    public IndicatorHierarchyInfo findIndicatorHierarchy(String indicatorId, List<ModelRequireBaseInfo> dms) {
        if (StringUtils.isBlank(indicatorId) || CollectionUtils.isEmpty(dms)) {
            return null;
        }

        for (ModelRequireBaseInfo dim : dms) {
            IndicatorHierarchyInfo hierarchyInfo = findIndicatorInDimension(indicatorId, dim);
            if (hierarchyInfo != null) {
                return hierarchyInfo;
            }
        }

        return null;
    }

    /**
     * 在指定维度中查找指标的层级关系
     */
    @Nullable
    private IndicatorHierarchyInfo findIndicatorInDimension(String indicatorId, ModelRequireBaseInfo dim) {
        // 如果是末级维度，查找其下的指标
        if (Integer.valueOf(1).equals(dim.getLevelType()) && CollectionUtils.isNotEmpty(dim.getDetails())) {
            for (ModelRequireDetailInfo detail : dim.getDetails()) {
                IndicatorHierarchyInfo hierarchyInfo = findIndicatorInDetail(indicatorId, detail, dim, new ArrayList<>());
                if (hierarchyInfo != null) {
                    return hierarchyInfo;
                }
            }
        }

        // 递归查找子维度
        if (CollectionUtils.isNotEmpty(dim.getChilds())) {
            for (ModelRequireBaseInfo childDim : dim.getChilds()) {
                IndicatorHierarchyInfo hierarchyInfo = findIndicatorInDimension(indicatorId, childDim);
                if (hierarchyInfo != null) {
                    return hierarchyInfo;
                }
            }
        }

        return null;
    }

    /**
     * 在指标详情中查找目标指标
     */
    @Nullable
    private IndicatorHierarchyInfo findIndicatorInDetail(
            String indicatorId, ModelRequireDetailInfo detail, ModelRequireBaseInfo dim, List<ModelRequireDetailInfo> parentIndicators) {
        // 如果找到目标指标
        if (indicatorId.equals(detail.getItemId())) {
            IndicatorHierarchyInfo hierarchyInfo = new IndicatorHierarchyInfo();
            hierarchyInfo.setDimId(dim.getDmId());
            hierarchyInfo.setDimName(dim.getDmName());

            // 根据层级设置指标信息
            if (parentIndicators.isEmpty()) {
                // 一级指标
                hierarchyInfo.setFirstIndicatorId(detail.getItemId());
                hierarchyInfo.setFirstIndicatorName(resolveIndicatorName(detail));
            } else if (parentIndicators.size() == 1) {
                // 二级指标
                ModelRequireDetailInfo parent = parentIndicators.get(0);
                hierarchyInfo.setFirstIndicatorId(parent.getItemId());
                hierarchyInfo.setFirstIndicatorName(resolveIndicatorName(parent));
                hierarchyInfo.setSecondIndicatorId(detail.getItemId());
                hierarchyInfo.setSecondIndicatorName(resolveIndicatorName(detail));
            } else if (parentIndicators.size() == 2) {
                // 三级指标
                ModelRequireDetailInfo grandParent = parentIndicators.get(0);
                ModelRequireDetailInfo parent = parentIndicators.get(1);
                hierarchyInfo.setFirstIndicatorId(grandParent.getItemId());
                hierarchyInfo.setFirstIndicatorName(resolveIndicatorName(grandParent));
                hierarchyInfo.setSecondIndicatorId(parent.getItemId());
                hierarchyInfo.setSecondIndicatorName(resolveIndicatorName(parent));
                hierarchyInfo.setThirdIndicatorId(detail.getItemId());
                hierarchyInfo.setThirdIndicatorName(resolveIndicatorName(detail));
            }

            return hierarchyInfo;
        }

        // 递归查找子指标
        if (CollectionUtils.isNotEmpty(detail.getChilds())) {
            List<ModelRequireDetailInfo> newParentIndicators = new ArrayList<>(parentIndicators);
            newParentIndicators.add(detail);

            for (ModelRequireDetailInfo child : detail.getChilds()) {
                IndicatorHierarchyInfo hierarchyInfo = findIndicatorInDetail(indicatorId, child, dim, newParentIndicators);
                if (hierarchyInfo != null) {
                    return hierarchyInfo;
                }
            }
        }

        return null;
    }

    /**
     * 名称解析逻辑：
     *  - itemType==0：使用 itemValue
     *  - itemType==4：使用 rt.name
     *  - 其他：使用 item.name
     */
    private String resolveIndicatorName(ModelRequireDetailInfo info) {
        if (info == null) {
            return "";
        }
        Integer type = info.getItemType();
        if (type != null) {
            if (Integer.valueOf(0).equals(type)) {
                return info.getItemValue();
            }
            if (Integer.valueOf(4).equals(type)) {
                return info.getRt() != null ? info.getRt().getName() : "";
            }
            return info.getItem() != null ? info.getItem().getName() : "";
        }
        // 若类型为空，保持与历史兼容，回退到 itemValue
        return info.getItemValue();
    }

    /**
     * 数据转换器接口
     */
    public interface IndicatorDataConverter<T> {
        /**
         * 将原始数据转换为通用DTO
         */
        AuthPrjIndicatorDetailDTO convertToDTO(T source);

        /**
         * 获取原始指标ID
         */
        String getOriginalIndicatorId(T source);
    }

    /**
     * 通用的按维度分组处理方法
     *
     * @param context   指标处理上下文
     * @param dataList  原始数据列表
     * @param converter 数据转换器
     * @param <T>       原始数据类型
     * @return 按维度分组的结果
     */
    public <T> List<AuthPrjDimGroupDTO> groupIndicatorsByDimension(
            IndicatorProcessContext context, List<T> dataList, IndicatorDataConverter<T> converter) {

        // 按维度分组
        Map<String, List<AuthPrjIndicatorDetailDTO>> dimGroupMap = new HashMap<>();

        // 将数据按维度分组
        for (T data : dataList) {
            // 转换为通用DTO
            AuthPrjIndicatorDetailDTO dto = converter.convertToDTO(data);

            // 通过模型信息确定指标的层级关系和所属维度
            String originalIndicatorId = converter.getOriginalIndicatorId(data);
            IndicatorHierarchyInfo hierarchyInfo = findIndicatorHierarchy(originalIndicatorId, context.getModelInfoDTO().getDms());

            if (hierarchyInfo != null) {
                // 填充层级信息
                fillIndicatorHierarchyInfo(dto, hierarchyInfo);

                // 填充活动名称（数据来源）
                fillActivityName(dto, originalIndicatorId, context.getIndicatorToActivityMap());

                // 按维度分组
                dimGroupMap.computeIfAbsent(hierarchyInfo.getDimId(), k -> new ArrayList<>()).add(dto);
            }
        }

        // 构建结果
        List<AuthPrjDimGroupDTO> result = new ArrayList<>();
        for (Map.Entry<String, List<AuthPrjIndicatorDetailDTO>> entry : dimGroupMap.entrySet()) {
            String dimId = entry.getKey();
            List<AuthPrjIndicatorDetailDTO> indicators = entry.getValue();

            if (CollectionUtils.isNotEmpty(indicators)) {
                ModelRequireBaseInfo dimInfo = context.getDimMap().get(dimId);
                if (dimInfo != null) {
                    AuthPrjDimGroupDTO dimGroup = new AuthPrjDimGroupDTO();
                    dimGroup.setSdDimId(dimId);
                    dimGroup.setSdDimName(dimInfo.getDmName());
                    dimGroup.setIndicatorList(indicators);
                    result.add(dimGroup);
                }
            }
        }

        return result;
    }

    /**
     * 填充指标层级信息
     */
    private void fillIndicatorHierarchyInfo(AuthPrjIndicatorDetailDTO dto, IndicatorHierarchyInfo hierarchyInfo) {
        dto.setFirstIndicatorId(hierarchyInfo.getFirstIndicatorId());
        dto.setFirstIndicatorName(hierarchyInfo.getFirstIndicatorName());
        dto.setSecondIndicatorId(hierarchyInfo.getSecondIndicatorId());
        dto.setSecondIndicatorName(hierarchyInfo.getSecondIndicatorName());
        dto.setThirdIndicatorId(hierarchyInfo.getThirdIndicatorId());
        dto.setThirdIndicatorName(hierarchyInfo.getThirdIndicatorName());
    }

    /**
     * 填充活动名称
     */
    private void fillActivityName(AuthPrjIndicatorDetailDTO dto, String originalIndicatorId,
                                 Map<String, AomActvExtBO> indicatorToActivityMap) {
        if (StringUtils.isNotBlank(originalIndicatorId)) {
            AomActvExtBO activity = indicatorToActivityMap.get(originalIndicatorId);
            if (activity != null && StringUtils.isNotBlank(activity.getActvRefName())) {
                dto.setRefName(activity.getActvRefName());
            }
        }
    }
}
