package com.yxt.talent.rv.application.expert.component;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.*;
import com.yxt.downfacade.dto.File4CreateFinishMq;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExportMqService;
import com.yxt.export.I18nComponent;
import com.yxt.spsdfacade.bean.spsd.Base4Facade;
import com.yxt.spsdfacade.bean.spsd.BaseDict4Facade;
import com.yxt.spsdfacade.bean.spsd.BaseRt4Facade;
import com.yxt.spsdfacade.bean.spsd.BaseSearch4Facade;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.application.expert.*;
import com.yxt.talent.rv.application.expert.export.ExpertExportStrategy;
import com.yxt.talent.rv.application.expert.export.ExpertImportStrategy;
import com.yxt.talent.rv.controller.common.viewobj.EntityIdVOName;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.NumberEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertIndicatorMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.authprj.ExpertMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertDeptPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.authprj.ExpertPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.dto.FileImportResult;
import com.yxt.talentrvfacade.bean.Exper4Facade;
import com.yxt.talentrvfacade.bean.ExpertDto;
import com.yxt.talentrvfacade.bean.ExpertResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport.MAX_LEASE_TIME;

@Slf4j
@RequiredArgsConstructor
@Service
public class ExpertComponent {

    private final ExpertMapper expertMapper;
    private final I18nComponent i18nComponent;
    private final ExpertDeptMapper expertDeptMapper;
    private final ExpertIndicatorMapper expertIndicatorMapper;
    private final SptalentsdFacade sptalentsdFacade;
    private final ILock lockService;
    private final DlcComponent dlcComponent;
    private final ExportMqService exportMqService;
    private final ExpertImportStrategy expertImportStrategy;
    private final ExpertExportStrategy expertExportStrategy;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final UdpDeptMapper udpDeptMapper;

    private static final String TASK_NAME = "apis.sptalentrv.expert.export.task.name";
    private static final String IMPORT_TMP_TASK_NAME = "apis.sptalentrv.expert.import.tmp.export.task.name";


    public PagingList<ExpertPageVO> page(PageRequest pageRequest, ExpertSearchCriteria searchParam) {
        Page<ExpertPO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        if (StringUtils.isNotBlank(searchParam.getKeyword())) {
            searchParam.setKeyword(ApiUtil.getFiltedLikeString(searchParam.getKeyword()));
        }

        IPage<ExpertPO> expertPOPagingList = expertMapper.pageList(page, searchParam);
        if (null == expertPOPagingList || CollectionUtils.isEmpty(expertPOPagingList.getRecords())) {
            return new PagingList<>();
        }

        List<String> expertIds = expertPOPagingList.getRecords().stream().map(ExpertPO::getId).collect(Collectors.toList());
        List<ExpertDeptPO> expertDeptPOS = expertDeptMapper.findByOrgIdAndExpertIdIn(searchParam.getOrgId(), expertIds);
        if (CollectionUtils.isNotEmpty(expertDeptPOS)) {
            Map<String, List<ExpertDeptPO>> expertDepts = expertDeptPOS.stream().collect(Collectors.groupingBy(ExpertDeptPO::getExpertId));
            expertPOPagingList.getRecords().forEach(e -> {
                List<ExpertDeptPO> pos = expertDepts.get(e.getId());
                if (null != pos) {
                    e.setDepts(pos.stream().map(ExpertDeptPO::getDeptName).collect(Collectors.toList()));
                    e.setDeptIds(pos.stream().map(ExpertDeptPO::getDeptId).collect(Collectors.toList()));
                }
            });
        }

        List<ExpertIndicatorPO> expertIndPOS = expertIndicatorMapper.findByOrgIdAndExpertIdIn(searchParam.getOrgId(), expertIds);
        if (CollectionUtils.isNotEmpty(expertIndPOS)) {
            Map<String, List<ExpertIndicatorPO>> expertInds = expertIndPOS.stream().collect(Collectors.groupingBy(ExpertIndicatorPO::getExpertId));
            expertPOPagingList.getRecords().forEach(e -> {
                List<ExpertIndicatorPO> pos = expertInds.get(e.getId());
                if (null != pos) {
                    List<String> inIds = pos.stream().map(ExpertIndicatorPO::getIndId).collect(Collectors.toList());
                    BaseSearch4Facade search = new BaseSearch4Facade();
                    search.setIds(inIds);
                    search.setOrgId(searchParam.getOrgId());

                    List<String> inNames = new ArrayList<>();
                    List<String> inNums = new ArrayList<>();
                    Base4Facade base4Facade = sptalentsdFacade.getBaseInfo(search);
                    if (CollectionUtils.isNotEmpty(base4Facade.getSkills())) {
                        inNames.addAll(base4Facade.getSkills().stream().map(BaseDict4Facade::getName).collect(Collectors.toList()));
                        inNums.addAll(base4Facade.getSkills().stream().map(BaseDict4Facade::getNum).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(base4Facade.getTechs())) {
                        inNames.addAll(base4Facade.getTechs().stream().map(BaseDict4Facade::getName).collect(Collectors.toList()));
                        inNums.addAll(base4Facade.getTechs().stream().map(BaseDict4Facade::getNum).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(base4Facade.getKngs())) {
                        inNames.addAll(base4Facade.getKngs().stream().map(BaseDict4Facade::getName).collect(Collectors.toList()));
                        inNums.addAll(base4Facade.getKngs().stream().map(BaseDict4Facade::getNum).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(base4Facade.getRts())) {
                        inNames.addAll(base4Facade.getRts().stream().map(BaseRt4Facade::getName).collect(Collectors.toList()));
                        inNums.addAll(base4Facade.getRts().stream().map(BaseRt4Facade::getNum).collect(Collectors.toList()));
                    }
                    e.setIndicators(inNames);
                    e.setIndicatorNums(inNums);
                }
            });
        }

        List<ExpertPageVO> result = BeanCopierUtil.convertList(expertPOPagingList.getRecords(), ExpertPO.class, ExpertPageVO.class);
        PagingList<ExpertPageVO> getPagingList = new PagingList<>();
        getPagingList.setPaging(BeanCopierUtil.toPagingList(expertPOPagingList).getPaging());
        getPagingList.setDatas(result);
        return getPagingList;
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String create(String orgId, ExpertCreateDTO createDTO, String userId) {
        ExpertPO oldExpert = expertMapper.findByOrgIdAndUserId(orgId, createDTO.getUserId());
        if (null != oldExpert && StringUtils.isNotBlank(oldExpert.getId())) {
            throw new ApiException(ExceptionKeys.EXPERT_USER_REPEAT);
        }
        ExpertPO expertPO = new ExpertPO();
        expertPO.setId(ApiUtil.getUuid());
        expertPO.setOrgId(orgId);
        expertPO.setRemark(createDTO.getRemark());
        expertPO.setUserId(createDTO.getUserId());
        expertPO.setDeleted(YesOrNo.NO.getValue());
        expertPO.setCreateUserId(userId);
        expertPO.setUpdateUserId(userId);
        expertPO.setCreateTime(LocalDateTime.now());
        expertPO.setUpdateTime(LocalDateTime.now());
        expertMapper.insert(expertPO);

        if (CollectionUtils.isNotEmpty(createDTO.getDeptIds())) {
            List<ExpertDeptPO> expertDeptPOS = new ArrayList<>();
            for (String deptId : createDTO.getDeptIds()) {
                ExpertDeptPO expertDeptPO = new ExpertDeptPO();
                expertDeptPO.setExpertId(expertPO.getId());
                expertDeptPO.setOrgId(orgId);
                expertDeptPO.setDeleted(YesOrNo.NO.getValue());
                expertDeptPO.setId(ApiUtil.getUuid());
                expertDeptPO.setDeptId(deptId);
                expertDeptPO.setCreateUserId(userId);
                expertDeptPO.setUpdateUserId(userId);
                expertDeptPO.setCreateTime(LocalDateTime.now());
                expertDeptPO.setUpdateTime(LocalDateTime.now());
                expertDeptPOS.add(expertDeptPO);
            }
            expertDeptMapper.batchInsertOrUpdate(expertDeptPOS);
        }

        if (CollectionUtils.isNotEmpty(createDTO.getIndicatorIds())) {
            List<ExpertIndicatorPO> expertIndicatorPOS = new ArrayList<>();
            for (String inds : createDTO.getIndicatorIds()) {
                ExpertIndicatorPO expertIndicatorPO = new ExpertIndicatorPO();
                expertIndicatorPO.setExpertId(expertPO.getId());
                expertIndicatorPO.setOrgId(orgId);
                expertIndicatorPO.setDeleted(YesOrNo.NO.getValue());
                expertIndicatorPO.setId(ApiUtil.getUuid());
                expertIndicatorPO.setIndId(inds);
                expertIndicatorPO.setCreateUserId(userId);
                expertIndicatorPO.setUpdateUserId(userId);
                expertIndicatorPO.setCreateTime(LocalDateTime.now());
                expertIndicatorPO.setUpdateTime(LocalDateTime.now());
                expertIndicatorPOS.add(expertIndicatorPO);
            }
            expertIndicatorMapper.batchInsertOrUpdate(expertIndicatorPOS);
        }

        return expertPO.getId();
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void update(String orgId, ExpertCreateDTO createDTO, String userId, String id) {
        ExpertPO expertPO = expertMapper.selectById(id);
        if (null == expertPO || expertPO.getDeleted() == 1) {
            throw new ApiException(ExceptionKeys.EXPERT_NOTEXISTS);
        }

        expertPO.setRemark(createDTO.getRemark());
        expertPO.setUpdateUserId(userId);
        expertPO.setUpdateTime(LocalDateTime.now());
        expertMapper.updateById(expertPO);

        expertDeptMapper.deleteByExpertId(orgId, id);
        expertIndicatorMapper.deleteByExpertId(orgId, id);

        if (CollectionUtils.isNotEmpty(createDTO.getDeptIds())) {
            List<ExpertDeptPO> expertDeptPOS = new ArrayList<>();
            for (String deptId : createDTO.getDeptIds()) {
                ExpertDeptPO expertDeptPO = new ExpertDeptPO();
                expertDeptPO.setExpertId(expertPO.getId());
                expertDeptPO.setOrgId(orgId);
                expertDeptPO.setDeleted(YesOrNo.NO.getValue());
                expertDeptPO.setId(ApiUtil.getUuid());
                expertDeptPO.setDeptId(deptId);
                expertDeptPO.setCreateUserId(userId);
                expertDeptPO.setUpdateUserId(userId);
                expertDeptPO.setCreateTime(LocalDateTime.now());
                expertDeptPO.setUpdateTime(LocalDateTime.now());
                expertDeptPOS.add(expertDeptPO);
            }
            expertDeptMapper.batchInsertOrUpdate(expertDeptPOS);
        }

        if (CollectionUtils.isNotEmpty(createDTO.getIndicatorIds())) {
            List<ExpertIndicatorPO> expertIndicatorPOS = new ArrayList<>();
            for (String inds : createDTO.getIndicatorIds()) {
                ExpertIndicatorPO expertIndicatorPO = new ExpertIndicatorPO();
                expertIndicatorPO.setExpertId(expertPO.getId());
                expertIndicatorPO.setOrgId(orgId);
                expertIndicatorPO.setDeleted(YesOrNo.NO.getValue());
                expertIndicatorPO.setId(ApiUtil.getUuid());
                expertIndicatorPO.setIndId(inds);
                expertIndicatorPO.setCreateUserId(userId);
                expertIndicatorPO.setUpdateUserId(userId);
                expertIndicatorPO.setCreateTime(LocalDateTime.now());
                expertIndicatorPO.setUpdateTime(LocalDateTime.now());
                expertIndicatorPOS.add(expertIndicatorPO);
            }
            expertIndicatorMapper.batchInsertOrUpdate(expertIndicatorPOS);
        }
    }

    public void deleteById(String id, String userId) {
        ExpertPO expertPO = expertMapper.selectById(id);
        if (null == expertPO || expertPO.getDeleted() == 1) {
            return;
        }

        expertPO.setDeleted(YesOrNo.YES.getValue());
        EntityUtil.setUpdatedInfo(userId, expertPO);
        expertMapper.updateById(expertPO);
    }

    public ExpertGetVO findOne(String orgId, String id) {
        ExpertPO expertPO = expertMapper.selectById(id);
        if (null == expertPO || expertPO.getDeleted() == 1) {
            throw new ApiException(ExceptionKeys.EXPERT_NOTEXISTS);
        }

        ExpertGetVO result = new ExpertGetVO();
        result.setId(id);
        result.setUserId(expertPO.getUserId());
        result.setRemark(expertPO.getRemark());

        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectById(expertPO.getUserId());
        if (null != udpLiteUserPO) {
            result.setFullname(udpLiteUserPO.getFullname());
        }

        List<ExpertDeptPO> expertDeptPOS = expertDeptMapper.findByOrgIdAndExpertIdIn(orgId, Arrays.asList(result.getId()));
        if (CollectionUtils.isNotEmpty(expertDeptPOS)) {
            List<EntityIdVOName> depts = new ArrayList<>();
            for (ExpertDeptPO expertDeptPO : expertDeptPOS) {
                EntityIdVOName dept = new EntityIdVOName();
                dept.setName(expertDeptPO.getDeptName());
                dept.setId(expertDeptPO.getDeptId());
                depts.add(dept);
            }
            result.setDepts(depts);
        }

        List<ExpertIndicatorPO> expertIndPOS = expertIndicatorMapper.findByOrgIdAndExpertIdIn(orgId, Arrays.asList(result.getId()));
        if (CollectionUtils.isNotEmpty(expertIndPOS)) {
            List<String> inIds = expertIndPOS.stream().map(ExpertIndicatorPO::getIndId).collect(Collectors.toList());
            BaseSearch4Facade search = new BaseSearch4Facade();
            search.setIds(inIds);
            search.setOrgId(orgId);

            List<EntityIdVOName> inNames = new ArrayList<>();
            Base4Facade base4Facade = sptalentsdFacade.getBaseInfo(search);
            if (CollectionUtils.isNotEmpty(base4Facade.getSkills())) {
                for (BaseDict4Facade skill : base4Facade.getSkills()) {
                    EntityIdVOName idVOName = new EntityIdVOName();
                    idVOName.setId(skill.getId());
                    idVOName.setName(skill.getName());
                    idVOName.setType(1);
                    inNames.add(idVOName);
                }
            }
            if (CollectionUtils.isNotEmpty(base4Facade.getTechs())) {
                for (BaseDict4Facade tech : base4Facade.getTechs()) {
                    EntityIdVOName idVOName = new EntityIdVOName();
                    idVOName.setId(tech.getId());
                    idVOName.setName(tech.getName());
                    idVOName.setType(2);
                    inNames.add(idVOName);
                }
            }
            if (CollectionUtils.isNotEmpty(base4Facade.getKngs())) {
                for (BaseDict4Facade kng : base4Facade.getKngs()) {
                    EntityIdVOName idVOName = new EntityIdVOName();
                    idVOName.setId(kng.getId());
                    idVOName.setName(kng.getName());
                    idVOName.setType(3);
                    inNames.add(idVOName);
                }
            }
            if (CollectionUtils.isNotEmpty(base4Facade.getRts())) {
                for (BaseRt4Facade rt : base4Facade.getRts()) {
                    EntityIdVOName idVOName = new EntityIdVOName();
                    idVOName.setId(rt.getId());
                    idVOName.setName(rt.getName());
                    idVOName.setType(4);
                    inNames.add(idVOName);
                }
            }

            Map<String, EntityIdVOName> inNameMap = StreamUtil.list2map(inNames, EntityIdVOName::getId);
            List<EntityIdVOName> indicators = new ArrayList<>();
            for (ExpertIndicatorPO expertIndPO : expertIndPOS) {
                if (null == inNameMap.get(expertIndPO.getIndId())) {
                    continue;
                }
                EntityIdVOName indicator = new EntityIdVOName();
                indicator.setName(inNameMap.get(expertIndPO.getIndId()).getName());
                indicator.setId(expertIndPO.getIndId());
                indicator.setType(inNameMap.get(expertIndPO.getIndId()).getType());
                indicators.add(indicator);
            }
            result.setIndicators(indicators);
        }
        return result;
    }

    public Map<String, String> export(String orgId, String userId, ExpertSearchCriteria searchParam) {
        String lockKey = String.format(RedisKeys.CACHE_KEY_EXPERT_EXPORT, orgId, userId);
        String fileName = "";
        String downFileName = "";
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                PageRequest pageRequest = new PageRequest(0, 10000, "", "");
                searchParam.setOrgId(orgId);
                PagingList<ExpertPageVO> page = page(pageRequest, searchParam);
                List<ExpertExportVO> exports = new ArrayList<>();
                for (ExpertPageVO data : page.getDatas()) {
                    ExpertExportVO exportVO = new ExpertExportVO();
                    BeanHelper.copyProperties(data, exportVO);
                    if (CollectionUtils.isNotEmpty(data.getDepts())) {
                        exportVO.setDeptList(exportDeptNameChain(orgId, data.getDeptIds()));
                    }
                    exportVO.setIndicatorList(String.join("，", data.getIndicators()));
                    exportVO.setIndicatorNumList(String.join("，", data.getIndicatorNums()));
                    exports.add(exportVO);
                }

                fileName = i18nComponent.getI18nValue(TASK_NAME)
                           + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;
                downFileName = fileName + FileConstants.ORIG;

                long taskId = dlcComponent.prepareExport(fileName, expertExportStrategy);
                dlcComponent.upload2Disk(fileName, exports, expertExportStrategy, taskId);
                File4CreateFinishMq file4CreateFinishMq = new File4CreateFinishMq();
                file4CreateFinishMq.setId(String.valueOf(taskId));
                file4CreateFinishMq.setStatus(NumberEnum.ZERO.getNumber());
                file4CreateFinishMq.setFileName(downFileName);
                file4CreateFinishMq.setGenerateFinishTime(DateUtil.now());
                file4CreateFinishMq.setLocalPath(fileName);
                exportMqService.sendMq4FileCreateFinish(file4CreateFinishMq);
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }

        Map<String, String> map = new HashMap<>();
        map.put("file", fileName);
        return map;
    }

    private String exportDeptNameChain(String orgId, List<String> deptIds) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return "";
        }

        StringBuilder deptChainName = new StringBuilder();
        for (String deptId : deptIds) {
            UdpDeptPO deptPO = udpDeptMapper.selectByOrgIdAndId(orgId, deptId);
            if (null == deptPO) {
                continue;
            }

            List<String> deptChainIds = Arrays.asList(deptPO.getIdFullPath().split(";"));
            StringBuilder deptName = new StringBuilder();
            for (int i = 0; i < deptChainIds.size(); i++) {
                UdpDeptPO chainDeptPO = udpDeptMapper.selectByOrgIdAndId(orgId, deptChainIds.get(i));
                if (null == chainDeptPO || StringUtils.isBlank(chainDeptPO.getParentId())) {
                    continue;
                }

                if (i == deptChainIds.size() - 1) {
                    deptName.append(chainDeptPO.getName()).append(";");
                } else {
                    deptName.append(chainDeptPO.getName()).append("->");
                }
            }
            deptChainName.append(deptName);
        }

        return deptChainName.toString();
    }

    public void importTmpExport(String orgId, String userId) {
        String fileName = i18nComponent.getI18nValue(IMPORT_TMP_TASK_NAME)
                   + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;
        String downFileName = fileName + FileConstants.ORIG;

        long taskId = dlcComponent.prepareExport(fileName, expertImportStrategy);
        dlcComponent.upload2Disk(fileName, null, expertImportStrategy, taskId);
        File4CreateFinishMq file4CreateFinishMq = new File4CreateFinishMq();
        file4CreateFinishMq.setId(String.valueOf(taskId));
        file4CreateFinishMq.setStatus(NumberEnum.ZERO.getNumber());
        file4CreateFinishMq.setFileName(downFileName);
        file4CreateFinishMq.setGenerateFinishTime(DateUtil.now());
        file4CreateFinishMq.setLocalPath(fileName);
        exportMqService.sendMq4FileCreateFinish(file4CreateFinishMq);
    }

    public List<ExpertResp> findCorrectExperts(Exper4Facade req) {
        if (StringUtils.isBlank(req.getOrgId()) || StringUtils.isBlank(req.getUserId()) || CollectionUtils.isEmpty(req.getIndicatorIds())) {
            return new ArrayList<>();
        }

        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectById(req.getUserId());
        if (null == udpLiteUserPO) {
            return new ArrayList<>();
        }

        List<ExpertResp> resp = new ArrayList<>();
        for (String indicatorId : req.getIndicatorIds()) {
            loopCorrectExpert(req.getOrgId(), req.getUserId(), udpLiteUserPO.getDeptId(), indicatorId, resp);
        }

        return resp;
    }

    private void loopCorrectExpert(String orgId, String userId, String deptId, String indicatorId, List<ExpertResp> resp) {
        if (StringUtils.isBlank(deptId)) {
            return;
        }

        List<ExpertPO> expertPOS = expertMapper.findCorrectExperts(orgId, deptId, indicatorId);
        if (CollectionUtils.isNotEmpty(expertPOS)) {
            ExpertResp expertResp = new ExpertResp();
            expertResp.setOrgId(orgId);
            expertResp.setUserId(userId);
            expertResp.setIndicatorId(indicatorId);
            expertResp.setExperts(BeanCopierUtil.convertList(expertPOS, ExpertPO.class, ExpertDto.class));
            resp.add(expertResp);
            return;
        }

        UdpDeptPO deptPO = udpDeptMapper.selectByOrgIdAndId(orgId, deptId);
        if (null == deptPO || StringUtils.isBlank(deptPO.getParentId())) {
            return;
        }

        loopCorrectExpert(orgId, userId, deptPO.getParentId(), indicatorId, resp);
    }

}
