#############################################################################################
###                                    导入导出国际化                                       ###
#############################################################################################
#评估人员导入模板
apis.sptalentrv.eval.user.export.header.fullName=被评估人姓名
apis.sptalentrv.eval.user.export.header.userName=被评估账号
apis.sptalentrv.eval.user.export.header.relationType=评估关系
apis.sptalentrv.eval.user.export.header.evaluatorFullName=评估人姓名
apis.sptalentrv.eval.user.export.header.evaluatorIdUserName=评估人账号
apis.sptalentrv.eval.user.export.file.name=导入评估关系
apis.sptalentrv.performance.user.export.header.userName=账号
apis.sptalentrv.performance.user.export.header.fullName=姓名
apis.sptalentrv.performance.user.export.header.period=绩效周期
apis.sptalentrv.performance.user.export.header.level=绩效
apis.sptalentrv.performance.user.export.header.perfScore=绩效总分
apis.sptalentrv.performance.user.export.header.perfPoint=绩效得分
apis.sptalentrv.performance.user.export.header.errorMsg=错误信息
apis.sptalentrv.perf.user.export.file.name=绩效导入

apis.sptalentrv.perf.temp.export.file.name=绩效周期模板导出
apis.sptalentrv.perf.user.export.header.fullName=员工姓名
apis.sptalentrv.perf.user.export.header.userName=员工账号
apis.sptalentrv.perf.user.export.header.period=绩效周期
apis.sptalentrv.perf.user.export.header.level=绩效
apis.sptalentrv.perf.user.export.header.perfScore=绩效总分
apis.sptalentrv.perf.user.export.header.perfPoint=绩效得分


apis.sptalentrv.team.rv.detail.export.file.name=动态人岗匹配导出人员明细
apis.sptalentrv.team.rv.detail.export.sheet1.name=维度达标率
apis.sptalentrv.team.rv.detail.export.sheet1.header.cataName=维度分类
apis.sptalentrv.team.rv.detail.export.sheet1.header.dimName=维度名称
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedUserCount=达标人数
apis.sptalentrv.team.rv.detail.export.sheet1.header.unAchievedUserCount=未达标人数
apis.sptalentrv.team.rv.detail.export.sheet1.header.achievedRate=达标率
apis.sptalentrv.team.rv.detail.export.sheet2.name=学员达标明细
apis.sptalentrv.team.rv.detail.export.sheet2.header.cataName=维度分类
apis.sptalentrv.team.rv.detail.export.sheet2.header.dimName=维度名称
apis.sptalentrv.team.rv.detail.export.sheet2.header.fullname=人员姓名
apis.sptalentrv.team.rv.detail.export.sheet2.header.username=人员账号
apis.sptalentrv.team.rv.detail.export.sheet2.header.achieved=是否达标

#xpd人员结果明细导出
apis.sptalentrv.xpd.user_result.export.file.name=学员盘点结果导出
apis.sptalentrv.xpd.user_result.export.sheet1.fullname=姓名
apis.sptalentrv.xpd.user_result.export.sheet1.username=账号
apis.sptalentrv.xpd.user_result.export.sheet1.status=账号状态
apis.sptalentrv.xpd.user_result.export.sheet1.dept=部门
apis.sptalentrv.xpd.user_result.export.sheet1.pos=岗位
apis.sptalentrv.xpd.user_result.export.sheet1.grade=职级
apis.sptalentrv.xpd.user_result.export.sheet1.join_type=加入方式
apis.sptalentrv.xpd.user_result.export.sheet1.join_time=加入时间
apis.sptalentrv.xpd.user_result.export.sheet1.xpd_progress=盘点进度
apis.sptalentrv.xpd.user_result.export.sheet1.result=盘点结果
apis.sptalentrv.xpd.user_result.export.sheet1.level=分层结果
apis.sptalentrv.xpd.user_result.export.sheet1.score_ratio=得分/达标率

#xpd落位宫格结果导出
apis.sptalentrv.xpd.dimcomb.result.file.name=宫格落位结果导出
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.fullname=姓名
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.username=账号
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.status=账号状态
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.dept=部门
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.pos=岗位
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.grade=职级
apis.sptalentrv.xpd.dimcomb.result.export.sheet1.grid_cell=落位宫格

#xpd人才分层结果导出
apis.sptalentrv.xpd.result.file.name=人才分层结果导出
apis.sptalentrv.xpd.result.export.sheet1.fullname=姓名
apis.sptalentrv.xpd.result.export.sheet1.username=账号
apis.sptalentrv.xpd.result.export.sheet1.status=账号状态
apis.sptalentrv.xpd.result.export.sheet1.dept=部门
apis.sptalentrv.xpd.result.export.sheet1.pos=岗位
apis.sptalentrv.xpd.result.export.sheet1.grade=职级
apis.sptalentrv.xpd.result.export.sheet1.level=人才分层
apis.sptalentrv.xpd.result.export.sheet1.result=项目结果

#xpd维度分层结果导出
apis.sptalentrv.xpd.dim.result.file.name=维度分层结果导出
apis.sptalentrv.xpd.dim.result.export.sheet1.fullname=姓名
apis.sptalentrv.xpd.dim.result.export.sheet1.username=账号
apis.sptalentrv.xpd.dim.result.export.sheet1.status=账号状态
apis.sptalentrv.xpd.dim.result.export.sheet1.dept=部门
apis.sptalentrv.xpd.dim.result.export.sheet1.pos=岗位
apis.sptalentrv.xpd.dim.result.export.sheet1.grade=职级
apis.sptalentrv.xpd.dim.result.export.sheet1.level=维度分层
apis.sptalentrv.xpd.dim.result.export.sheet1.result=维度结果


#校准导入
apis.sptalentrv.cali.user.error.file=导入校准人员错误
apis.sptalentrv.cali.userName=员工账号
apis.sptalentrv.cali.fullName=员工姓名
apis.sptalentrv.grid.cell.name.1=1号位
apis.sptalentrv.grid.cell.name.2=2号位
apis.sptalentrv.grid.cell.name.3=3号位
apis.sptalentrv.grid.cell.name.4=4号位
apis.sptalentrv.grid.cell.name.5=5号位
apis.sptalentrv.grid.cell.name.6=6号位
apis.sptalentrv.grid.cell.name.7=7号位
apis.sptalentrv.grid.cell.name.8=8号位
apis.sptalentrv.grid.cell.name.9=9号位
apis.sptalentrv.grid.cell.name.10=10号位
apis.sptalentrv.grid.cell.name.11=11号位
apis.sptalentrv.grid.cell.name.12=12号位
apis.sptalentrv.grid.cell.name.13=13号位
apis.sptalentrv.grid.cell.name.14=14号位
apis.sptalentrv.grid.cell.name.15=15号位
apis.sptalentrv.grid.cell.name.16=16号位

# 绩效活动
apis.sptalentrv.perf.template.export.file.remark=导入绩效模板说明\u000A1.带*的列为必填项，必须填写；\u000A2.绩效请在绩效设置中的枚举值范围进行选择填写；\u000A3.绩效等级和绩效得分，必须保证至少存在一个有效数据。
apis.sptalentrv.perf.template.export.file.header.username=员工账号*
apis.sptalentrv.perf.template.export.file.header.fullname=员工姓名
apis.sptalentrv.perf.template.export.file.header.preflevel=绩效等级
apis.sptalentrv.perf.template.export.file.header.perfsscore=绩效得分
apis.sptalentrv.perf.template.export.file.name=绩效导入模板
apis.sptalentrv.perf.template.export.file.header.errormsg=错误信息
apis.sptalentrv.perf.import.error.username=账号为空
apis.sptalentrv.perf.import.error.notexite=账号不存在
apis.sptalentrv.perf.import.error.not_in_prj=账号不在当前项目中
apis.sptalentrv.perf.import.error.userdelete=账号已被删除
apis.sptalentrv.perf.import.error.userforbidden=账号已被禁用
apis.sptalentrv.perf.import.error.period=绩效周期不存在
apis.sptalentrv.perf.import.error.perfgrade=绩效等级不存在或已被禁用

apis.perf.user.export.header.username=账号
apis.perf.user.export.header.fullname=姓名
apis.perf.user.export.header.userStatus=账号状态
apis.perf.user.export.header.deptName=所在部门
apis.perf.user.export.header.positionName=岗位
apis.perf.user.export.header.completeStatus=完成状态
apis.perf.user.export.header.evalResult=评估结果
apis.perf.user.export.header.quilified=是否达标

#导入活动
apis.sptalentrv.xpd.import.export.temp.remark0=导入维度指标结果模板说明
apis.sptalentrv.xpd.import.export.temp.remark1=1. 人员和维度信息会自动带出，不可修改
apis.sptalentrv.xpd.import.export.temp.remark2=2. 需要导入结果的人员，可填写指标结果后导入
apis.sptalentrv.xpd.import.export.temp.remark3=3. 指标得分仅支持999以内的数字，最多支持2位小数
apis.sptalentrv.xpd.import.export.temp.remark4=4. 得分和是否达标二选一必填
apis.sptalentrv.xpd.import.export.temp.fullname=姓名
apis.sptalentrv.xpd.import.export.temp.username=账号
apis.sptalentrv.xpd.import.export.temp.score=得分
apis.sptalentrv.xpd.import.export.temp.standard=是否达标
apis.sptalentrv.xpd.import.export.temp.dim.result=维度分层结果导入模板
apis.sptalentrv.xpd.import.export.temp.dim.indicator=维度指标明细导入模板

apis.sptalentrv.xpd.import.dim.result.errFile=维度分层导入错误导出
apis.sptalentrv.xpd.import.dim.indicator.errFile=维度指标导入错误导出
apis.sptalentrv.xpd.import.export.temp.dim.sheetName=潜力
apis.sptalentrv.xpd.import.export.temp.ind.sheetName=潜力
apis.sptalentrv.xpd.act.import.username.empty=请输入员工账号
apis.sptalentrv.xpd.act.import.username.error=请输入正确的员工账号
apis.sptalentrv.xpd.act.import.indicator.score.error=请输入正确的指标得分，允许最多2位小数的非负数
apis.sptalentrv.xpd.act.import.indicator.score.max.error=指标得分不能大于设置的指标总分
apis.sptalentrv.xpd.act.import.indicator.standard.yes=达标
apis.sptalentrv.xpd.act.import.indicator.standard.no=不达标
apis.sptalentrv.xpd.act.import.indicator.standard.error=请选择正确的是否达标结果
apis.sptalentrv.xpd.act.import.indicator.standard.score=分数和是否达标必填一个
apis.sptalentrv.xpd.act.import.indicator.standard.empty=是否达标必填
apis.sptalentrv.xpd.act.import.indicator.error=导入模板指标和维度指标不匹配
apis.sptalentrv.xpd.act.import.username.repeat=导入账号在excel中有重复
apis.sptalentrv.xpd.act.import.username.prj.not.exist=账号在盘点项目中不存在

apis.sptalentrv.xpd.act.import.level.remark0=导入维度等级结果模板说明
apis.sptalentrv.xpd.act.import.level.remark1=1. 人员和维度信息会自动带出，不可修改
apis.sptalentrv.xpd.act.import.level.remark2=2. 需要导入结果的人员，可选择其维度等级后导入
apis.sptalentrv.xpd.act.import.level.data=维度等级
apis.sptalentrv.xpd.act.import.level.name.empty=请输入维度等级
apis.sptalentrv.xpd.act.import.level.name.error=请选择正确的维度等级
apis.sptalentrv.xpd.act.import.level.errMsg=错误原因
apis.sptalentrv.xpd.grid.level.high=高
apis.sptalentrv.xpd.grid.level.middle=中
apis.sptalentrv.xpd.grid.level.low=低

apis.sptalentrv.xpd.grid.level.poor=较差
apis.sptalentrv.xpd.grid.level.general=一般
apis.sptalentrv.xpd.grid.level.good=良好
apis.sptalentrv.xpd.grid.level.excellent=优秀

#盘点校准导入
apis.sptalentrv.user.view.source.import.title=导入数据
apis.sptalentrv.user.view.source.data.title=个人档案

# 人才信息导入
apis.sptalentrv.user.ext.username.empty=账号必填
apis.sptalentrv.user.ext.username.repeat=账号重复
apis.sptalentrv.user.ext.username.exist=账号不存在
apis.sptalentrv.user.ext.dept.empty=部门必填
apis.sptalentrv.user.ext.actionName.empty=任职动作必填
apis.sptalentrv.user.ext.actionName.error=任职动作内容错误
apis.sptalentrv.user.ext.acqTimeStr.empty=任职动作发生时间必填
apis.sptalentrv.user.ext.acqTimeStr.error=任职动作发生时间格式错误
apis.sptalentrv.user.ext.length.200=长度超200
apis.sptalentrv.user.ext.manager.yes=是
apis.sptalentrv.user.ext.manager.no=否
apis.sptalentrv.user.ext.manager.empty=是否管理者不能为空
apis.sptalentrv.user.ext.manager.error=是否管理者内容错误
apis.sptalentrv.user.ext.occurrenceTime.empty=任职动作发生时间必填
apis.sptalentrv.user.ext.occurrenceTime.formate.error=任职动作发生时间格式错误
apis.sptalentrv.user.ext.rpTypeStr.yes=奖励
apis.sptalentrv.user.ext.rpTypeStr.no=惩罚
apis.sptalentrv.user.ext.rpName.empty=未填写奖惩名称
apis.sptalentrv.user.ext.rpTypeStr.empty=未填写奖惩类型
apis.sptalentrv.user.ext.rpTypeStr.error=奖惩类型不是下拉框中的值

apis.sptalentrv.user.ext.actionName.joining=入职
apis.sptalentrv.user.ext.actionName.promotion=晋升
apis.sptalentrv.user.ext.actionName.transfer=转岗



apis.sptalentrv.user.ext.reward.acqTimeStr.empty=未填写获得时间
apis.sptalentrv.user.ext.reward.acqTimeStr.error=获得时间格式不是日期
apis.sptalentrv.user.ext.import.err.filename=人才信息错误导出
apis.sptalentrv.user.ext.import.temp.filename=人才信息导入模板

apis.sptalentrv.user.ext.0.sheetName=人才信息
apis.sptalentrv.user.ext.0.title0=导入人才信息模板说明
apis.sptalentrv.user.ext.0.title1=1.带*的列为必填项，必须填写；
apis.sptalentrv.user.ext.0.title2=2.是否管理者支持选择“是”、“否”，必须输入或选择下拉框中规定的内容，不输入默认为否
apis.sptalentrv.user.ext.0.title3=3.职等、常驻地非必填，最多支持200个字符
apis.sptalentrv.user.ext.0.title4=4.职业资格证书非必填，支持录入多个证书，多个证书使用英文分号分割，最多支持200个字符
apis.sptalentrv.user.ext.0.username=账号
apis.sptalentrv.user.ext.0.fullname=姓名
apis.sptalentrv.user.ext.0.managerStr=是否管理者
apis.sptalentrv.user.ext.0.gradeLevel=职等
apis.sptalentrv.user.ext.0.profCerts=职业资格证书
apis.sptalentrv.user.ext.0.residenceAddress=常驻地
apis.sptalentrv.user.ext.0.errMsg=错误原因

apis.sptalentrv.user.ext.1.sheetName=内部任职履历
apis.sptalentrv.user.ext.1.title0=导入内部任职履历模板说明
apis.sptalentrv.user.ext.1.title1=1.带*的列为必填项，必须填写；
apis.sptalentrv.user.ext.1.title2=2.部门为必填，最多支持200个字符
apis.sptalentrv.user.ext.1.title3=3.岗位、职级非必填，最多支持200个字符
apis.sptalentrv.user.ext.1.title4=4.任职动作必填，可输入“入职”、“晋升”、“转岗”等关键动作，最多支持200个字符
apis.sptalentrv.user.ext.1.title5=5.任职动作发生时间格必填，式支持到日期，格式为YYYY-MM-DD或者YYYY/MM/DD
apis.sptalentrv.user.ext.1.username=账号
apis.sptalentrv.user.ext.1.fullname=姓名
apis.sptalentrv.user.ext.1.thirdDeptName=部门
apis.sptalentrv.user.ext.1.thirdPositionName=岗位
apis.sptalentrv.user.ext.1.thirdJobgradeName=职级
apis.sptalentrv.user.ext.1.actionName=任职动作
apis.sptalentrv.user.ext.1.occurrenceTime=任职动作发生时间
apis.sptalentrv.user.ext.1.errMsg=错误原因

apis.sptalentrv.user.ext.2.sheetName=奖罚信息
apis.sptalentrv.user.ext.2.title0=导入奖惩信息模板说明
apis.sptalentrv.user.ext.2.title1=1.带*的列为必填项，必须填写；
apis.sptalentrv.user.ext.2.title2=2奖惩名称为必填，最多支持200个字符
apis.sptalentrv.user.ext.2.title3=3.奖惩类型必填，支持选择“奖励”、“惩罚”，必须输入或选择下拉框中规定的内容
apis.sptalentrv.user.ext.2.title4=4.获得时间必填，格式支持到日期，格式为YYYY-MM-DD或者YYYY/MM/DD
apis.sptalentrv.user.ext.2.title5=5.发布方为非必填，最多支持200个字符
apis.sptalentrv.user.ext.2.username=账号
apis.sptalentrv.user.ext.2.fullname=姓名
apis.sptalentrv.user.ext.2.rpName=奖惩名称
apis.sptalentrv.user.ext.2.rpTypeStr=奖惩类型
apis.sptalentrv.user.ext.2.acqTimeStr=获得时间
apis.sptalentrv.user.ext.2.pubFrom=发布方
apis.sptalentrv.user.ext.2.errMsg=错误原因

apis.sptalentrv.grid.rule.level.count=等级维度数量
apis.sptalentrv.perf.rule.all.period=全部绩效周期
apis.sptalentrv.perf.rule.any.period=任意绩效周期
apis.sptalentrv.perf.rule.period.qty=任意x个绩效周期

apis.sptalentrv.xpd.judge.rule.radio=排名
apis.sptalentrv.xpd.judge.rule.score=得分
apis.sptalentrv.xpd.judge.rule.ptg=达标率

apis.sptalentrv.xpd.formula.score=得分
apis.sptalentrv.xpd.formula.import=导入数据
apis.sptalentrv.xpd.formula.profile=个人档案数据

apis.sptalentrv.eval.type_0=他评
apis.sptalentrv.eval.type_1=自评
apis.sptalentrv.eval.type_2=上级
apis.sptalentrv.eval.type_3=平级
apis.sptalentrv.eval.type_4=下级
apis.sptalentrv.eval.type_5=综合
apis.sptalentrv.eval.type_6=他评平均值
apis.sptalentrv.eval.type_7=他评总值
apis.sptalentrv.eval.type_8=自评平均值
apis.sptalentrv.eval.type_9=自评总值
apis.sptalentrv.eval.type_10=综合平均值
apis.sptalentrv.eval.type_11=综合总值

# 校准维度分层结果
apis.sptalentrv.cali.dimlevel.template.export.file.remark=导入校准结果模板说明：\u000A1、带*号的列为必填项\u000A2、校准后维度分层结果，必须与选项中可选内容一致，不可随意填写\u000A3、校准原因必填，最多支持2000个字符\u000A4、发展建议非必填，最多支持2000个字符
apis.sptalentrv.cali.dimlevel.template.export.file.header.username=账号*
apis.sptalentrv.cali.dimlevel.template.export.file.header.fullname=姓名
apis.sptalentrv.cali.dimlevel.template.export.file.header.reason=校准原因*
apis.sptalentrv.cali.dimlevel.template.export.file.header.recommend=发展建议
apis.sptalentrv.cali.dimlevel.template.export.file.header.error=错误原因
apis.sptalentrv.cali.dimlevel.template.export.file.name=校准维度分层结果模板
apis.sptalentrv.cali.dimlevel.template.import.error.file.name=导入校准维度分层结果
apis.sptalentrv.cali.dimlevel.template.template.export.file.header.errormsg=错误信息
apis.sptalentrv.cali.dimlevel.template.import.error.notexite=账号不存在
apis.sptalentrv.cali.dimlevel.template.import.error.resulterror=校准后维度分层错误
apis.sptalentrv.cali.dimlevel.template.import.error.reason.empty=校准原因必填
apis.sptalentrv.cali.dimlevel.template.import.error.cali.empty=尚未进行校准，请校准后保存
apis.sptalentrv.cali.dimlevel.template.import.error.disabled=账号已禁用
apis.sptalentrv.cali.dimlevel.template.import.error.noticali=当前用户不存在校准会中

# 校准维度结果
apis.sptalentrv.cali.dim.template.export.file.remark=导入校准结果模板说明：\u000A1、带*号的列为必填项\u000A2、校准后维度结果，跟随盘点的全局规则设置，与校准前结果格式一致，必须都是得分或者达标率，若是校准得分，则分数不可大于总分，若是达标率，则不可大于100%\u000A3、校准原因必填，最多支持2000个字符\u000A4、发展建议非必填，最多支持2000个字符
apis.sptalentrv.cali.dim.template.export.file.header.username=账号*
apis.sptalentrv.cali.dim.template.export.file.header.fullname=姓名
apis.sptalentrv.cali.dim.template.export.file.header.reason=校准原因*
apis.sptalentrv.cali.dim.template.export.file.header.recommend=发展建议
apis.sptalentrv.cali.dim.template.export.file.header.error=错误原因
apis.sptalentrv.cali.dim.template.export.file.name=校准维度结果模板
apis.sptalentrv.cali.dim.template.import.error.file.name=导入校准维度结果
apis.sptalentrv.cali.dim.template.template.export.file.header.errormsg=错误信息
apis.sptalentrv.cali.dim.template.import.error.notexite=账号不存在
apis.sptalentrv.cali.dim.template.import.error.resulterror=校准后维度结果错误
apis.sptalentrv.cali.dim.template.import.error.reason.empty=校准原因必填
apis.sptalentrv.cali.dim.template.import.error.score=得分不可大于总分
apis.sptalentrv.cali.dim.template.import.error.match=达标率不可大于100%
apis.sptalentrv.cali.dim.template.import.error.cali.empty=尚未进行校准，请校准后保存
apis.sptalentrv.cali.dim.template.import.error.disabled=账号已禁用
apis.sptalentrv.cali.dim.template.import.error.noticali=当前用户不存在校准会中

# 校准指标结果
apis.sptalentrv.cali.indicator.template.export.file.remark=导入校准结果模板说明：\u000A1、带*号的列为必填项\u000A2、校准后指标结果，跟随盘点的全局规则设置，与校准前结果格式一致，必须都是得分或者是否达标\u000A3、校准原因必填，最多支持2000个字符\u000A4、发展建议非必填，最多支持2000个字符
apis.sptalentrv.cali.indicator.template.export.file.header.username=账号*
apis.sptalentrv.cali.indicator.template.export.file.header.fullname=姓名
apis.sptalentrv.cali.indicator.template.export.file.header.reason=校准原因*
apis.sptalentrv.cali.indicator.template.export.file.header.recommend=发展建议
apis.sptalentrv.cali.indicator.template.export.file.header.error=错误原因
apis.sptalentrv.cali.indicator.template.export.file.cali.before=校准前
apis.sptalentrv.cali.indicator.template.export.file.cali.after=校准后
apis.sptalentrv.cali.indicator.template.export.file.name=校准指标结果模板
apis.sptalentrv.cali.indicator.template.import.error.file.name=导入校准指标结果
apis.sptalentrv.cali.indicator.template.import.error.notexite=账号不存在
apis.sptalentrv.cali.indicator.template.import.error.resulterror=校准后指标结果错误
apis.sptalentrv.cali.indicator.template.import.error.reason.empty=校准原因必填
apis.sptalentrv.cali.indicator.template.import.error.score=得分不可大于总分
apis.sptalentrv.cali.indicator.template.import.error.disabled=账号已禁用
apis.sptalentrv.cali.indicator.template.import.error.cali.empty=尚未进行校准，请校准后保存
apis.sptalentrv.cali.indicator.template.import.error.noticali=当前用户不存在校准会中

# 校准会记录
apis.sptalentrv.cali.record.user.deleted=已删除
apis.sptalentrv.cali.record.user.enable=启用
apis.sptalentrv.cali.record.user.disable=禁用

apis.sptalentrv.cali.record.user.fullname=姓名
apis.sptalentrv.cali.record.user.username=账号
apis.sptalentrv.cali.record.user.status=账号状态
apis.sptalentrv.cali.record.user.deptName=部门
apis.sptalentrv.cali.record.user.positionName=岗位
apis.sptalentrv.cali.record.user.gradeName=职级
apis.sptalentrv.cali.record.user.dimCombName=维度组合
apis.sptalentrv.cali.record.user.caliShiftStr=校准幅度
apis.sptalentrv.cali.record.user.before=校准前
apis.sptalentrv.cali.record.user.after=校准后
apis.sptalentrv.cali.record.user.before.cell=校准前宫格
apis.sptalentrv.cali.record.user.after.cell=校准后宫格
apis.sptalentrv.cali.record.user.caliUserName=校准人
apis.sptalentrv.cali.record.user.caliTime=校准时间
apis.sptalentrv.cali.record.user.reason=校准原因
apis.sptalentrv.cali.record.user.suggestion=发展建议

apis.sptalentrv.cali.record.dim.level=维度分层校准记录
apis.sptalentrv.cali.record.dim.result=维度结果校准记录
apis.sptalentrv.cali.record.ind.result=指标结果校准记录
apis.sptalentrv.cali.xpd.record.filename=盘点项目校准记录导出


#校准会人员导出
apis.sptalentrv.calimeet.user_data.export.file.name=校准会人员数据
apis.sptalentrv.calimeet.user_data.export.sheet1.fullname=姓名
apis.sptalentrv.calimeet.user_data.export.sheet1.username=账号
apis.sptalentrv.calimeet.user_data.export.sheet1.status=账号状态
apis.sptalentrv.calimeet.user_data.export.sheet1.dept=部门
apis.sptalentrv.calimeet.user_data.export.sheet1.pos=岗位
apis.sptalentrv.calimeet.user_data.export.sheet1.grade=职级
apis.sptalentrv.calimeet.user_data.export.sheet1.cali_status=校准状态
apis.sptalentrv.calimeet.user_data.export.sheet1.cali_time=校准时间
apis.sptalentrv.calimeet.user_data.export.sheet1.cali_user=校准人

apis.authprj.user.import.tem.file.name=认证人员导入模板
apis.authprj.user.export.header.username=账号*
apis.authprj.user.export.header.fullname=姓名
apis.authprj.user.export.header.remark=导入认证人员模板说明\u000A1.员工账号为必填项；\u000A2.姓名、账号不可超过200个字符；

apis.sptalentrv.authprj.user_result.export.file.name=认证人员结果导出
apis.sptalentrv.authprj.user_result.export.sheet1.fullname=姓名
apis.sptalentrv.authprj.user_result.export.sheet1.username=账号
apis.sptalentrv.authprj.user_result.export.sheet1.status=账号状态
apis.sptalentrv.authprj.user_result.export.sheet1.dept=部门
apis.sptalentrv.authprj.user_result.export.sheet1.pos=岗位
apis.sptalentrv.authprj.user_result.export.sheet1.grade=职级
apis.sptalentrv.authprj.user_result.export.sheet1.result=认证结果
apis.sptalentrv.authprj.user_result.export.sheet1.indresult=指标结果

apis.sptalentrv.expert.import.error.exists=专家已经存在
apis.sptalentrv.expert.import.error.repeat=账号重复
apis.sptalentrv.expert.import.error.nopermission=没有该员工的导入权限
apis.sptalentrv.expert.import.error.indempty=指标不允许为空
apis.sptalentrv.expert.import.error.indoversize=指标数量不可超过200个
apis.sptalentrv.expert.import.error.indnotexists=%s指标不存在或未启用
apis.sptalentrv.expert.import.error.deptoversize=部门数量不可超过20个
apis.sptalentrv.expert.import.error.deptnotexixts=%s部门不存在
apis.sptalentrv.expert.import.tmp.export.task.name=导入专家模板
apis.sptalentrv.expert.import.error.tmp.export.task.name=导入专家错误结果

# 认证项目 规则 选项中的 国际化
apis.sptalentrv.authprj.activity.project.score=项目得分
apis.sptalentrv.authprj.activity.result.suffix=结果
apis.sptalentrv.authprj.activity.score.suffix=得分
apis.sptalentrv.authprj.activity.result.pass=通过
apis.sptalentrv.authprj.activity.result.fail=不通过